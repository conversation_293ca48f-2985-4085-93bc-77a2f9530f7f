import React from 'react';
import { FaEye } from 'react-icons/fa';
import { HiUserCircle } from "react-icons/hi";
import { FaUserGroup } from "react-icons/fa6";
import LoadingAnimation from "../../../../Components/Loaders/LoadingAnimation";

/**
 * BulletinTableView Component
 * Table-style view for pending bulletins matching the design in the provided image
 */
const BulletinTableView = ({
  bulletins,
  loading,
  handleBulletinHistory
}) => {
  if (loading) {
    return (
      <div className="col-span-full flex justify-center items-center py-12">
        <LoadingAnimation />
      </div>
    );
  }

  // Format date and time
  const formatDateTime = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    const period = hours >= 12 ? 'pm' : 'am';
    return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
  };

  // Get tower and unit info
  const getTowerUnitInfo = (bulletin) => {
    if (bulletin.target_towers_data && bulletin.target_towers_data.length > 0) {
      // Show all towers if multiple, otherwise show the single tower
      const towerNames = bulletin.target_towers_data.map(tower =>
        tower.tower_name || tower.name || `Tower ${tower.id}`
      );
      const towerDisplay = towerNames.length > 1
        ? `${towerNames.slice(0, 2).join(', ')}${towerNames.length > 2 ? ` +${towerNames.length - 2} more` : ''}`
        : towerNames[0];

      const unit = bulletin.target_units_data && bulletin.target_units_data.length > 0
        ? bulletin.target_units_data[0]
        : null;

      return {
        tower: towerDisplay,
        unit: unit ? unit.unit_name || unit.unit_number || unit.id : ''
      };
    }
    return { tower: '', unit: '' };
  };

  // Get display name and icon based on post_as field
  const getDisplayInfo = (bulletin) => {
    console.log('[BulletinTableView] Bulletin data for display:', {
      id: bulletin.id,
      post_as: bulletin.post_as,
      postAs: bulletin.postAs,
      group_name: bulletin.group_name,
      member_name: bulletin.member_name,
      creator_name: bulletin.creator_name,
      creatorName: bulletin.creatorName,
      author: bulletin.author
    });

    const postAsValue = bulletin.post_as || bulletin.postAs;

    switch (postAsValue) {
      case 'group':
        return {
          name: bulletin.group_name || bulletin.author || 'Unknown Group',
          icon: <FaUserGroup className="w-6 h-6 text-gray-500" />
        };
      case 'member':
        return {
          name: bulletin.member_name || bulletin.author || 'Unknown Member',
          icon: <FaUserGroup className="w-6 h-6 text-gray-500" />
        };
      default: // 'creator' or any other value
        return {
          name: bulletin.creator_name || bulletin.creatorName || bulletin.author || 'Unknown User',
          icon: <HiUserCircle className="w-6 h-6 text-gray-500" />
        };
    }
  };



  return (
    <div className="w-full bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Table Header */}
      <div className="grid grid-cols-4 gap-6 px-6 py-4 border-b border-gray-200 text-sm font-semibold text-gray-700 bg-primaryLight">
        <div className="flex items-center">Name</div>
        <div className="flex items-center">Bulletin Title</div>
        <div className="flex items-center">Date & Time</div>
        <div className="flex items-center justify-center">Actions</div>
      </div>

      {/* Table Body */}
      <div className="divide-y divide-gray-100">
        {bulletins.map((bulletin, index) => {
          const { name, icon } = getDisplayInfo(bulletin);

          return (
            <div
              key={bulletin.id}
              className={`grid grid-cols-4 gap-6 px-6 py-5 items-center hover:bg-gray-50 transition-colors duration-200 ${
                index % 2 === 0 ? 'bg-white' : 'bg-gray-50/30'
              }`}
            >
              {/* Name - Shows based on post_as field */}
              <div className="flex items-center space-x-3 min-w-0">
                <div className="flex-shrink-0">
                  {icon}
                </div>
                <div className="min-w-0 flex-1">
                  <div className="text-sm font-medium text-gray-900 truncate" title={name}>
                    {name}
                  </div>
                </div>
              </div>

              {/* Bulletin Title */}
              <div className="min-w-0">
                <div className="text-sm text-gray-900 line-clamp-2 leading-relaxed" title={bulletin.title}>
                  {bulletin.title}
                </div>
              </div>

              {/* Date & Time - Shows when bulletin was created */}
              <div className="text-sm text-gray-600 font-medium">
                {formatDateTime(bulletin.created_at || bulletin.createdAt)}
              </div>

              {/* Actions */}
              <div className="flex items-center justify-center">
                <button
                  onClick={() => handleBulletinHistory(bulletin.id)}
                  className="inline-flex items-center justify-center w-8 h-8 rounded-full text-primary hover:text-primary/80 hover:bg-primary/10 transition-all duration-200"
                  title="View History"
                >
                  <FaEye className="w-4 h-4" />
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {bulletins.length === 0 && (
        <div className="flex flex-col justify-center items-center py-16 text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <FaEye className="w-6 h-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No pending bulletins found
          </h3>
          <p className="text-sm text-gray-500">
            Pending bulletins will appear here when they are submitted for review.
          </p>
        </div>
      )}
    </div>
  );
};

export default BulletinTableView;
